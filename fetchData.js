/**
 * Fetches data from a given URL using async/await, with error handling and fallback.
 * @param {string} url - The URL to fetch data from.
 * @param {any} fallbackValue - The value to return if the fetch operation fails.
 * @returns {Promise<any>} A promise that resolves with the fetched data or the fallback value.
 */
async function fetchData(url, fallbackValue = null) {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      // If the response is not OK (e.g., 404, 500), throw an error
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Failed to fetch data from ${url}:`, error);
    // Return the fallback value in case of an error
    return fallbackValue;
  }
}

// --- Example Usage ---
// You can replace this with any public API URL you want to test
const PUBLIC_API_URL = 'https://jsonplaceholder.typicode.com/posts/1';
const INVALID_API_URL = 'https://jsonplaceholder.typicode.com/nonexistent';

async function runExamples() {
  console.log('--- Fetching valid data ---');
  const post = await fetchData(PUBLIC_API_URL, { id: 'fallback', title: 'Fallback Post' });
  console.log('Fetched Data (Valid):', post);

  console.log('\n--- Fetching invalid data with fallback ---');
  const errorData = await fetchData(INVALID_API_URL, { error: true, message: 'Data could not be loaded.' });
  console.log('Fetched Data (Invalid/Fallback):', errorData);

  console.log('\n--- Fetching data from a non-existent domain (will likely throw a network error) ---');
  const networkErrorData = await fetchData('https://nonexistent-domain-12345.com/data', 'Network Error Fallback');
  console.log('Fetched Data (Network Error Fallback):', networkErrorData);
}

// Call the example function to see it in action
runExamples();