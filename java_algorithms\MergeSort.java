public class MergeSort {

    /**
     * Sorts an array of integers using the Merge Sort algorithm.
     * This is the main entry point for the merge sort.
     *
     * @param arr The array of integers to be sorted.
     */
    public static void mergeSort(int[] arr) {
        // If the array is null or has less than 2 elements, it's already sorted (or invalid),
        // so there's nothing to do.
        if (arr == null || arr.length < 2) {
            return;
        }
        // Call the recursive helper method to perform the sorting.
        sort(arr, 0, arr.length - 1);
    }

    /**
     * Recursive helper method to divide the array and sort its halves.
     *
     * @param arr   The array to be sorted.
     * @param left  The starting index of the subarray.
     * @param right The ending index of the subarray.
     */
    private static void sort(int[] arr, int left, int right) {
        // Base case: if the subarray has one or zero elements, it's considered sorted.
        if (left < right) {
            // Find the middle point to divide the array into two halves.
            int mid = left + (right - left) / 2;

            // Recursively sort the first half.
            sort(arr, left, mid);
            // Recursively sort the second half.
            sort(arr, mid + 1, right);

            // Merge the sorted halves.
            merge(arr, left, mid, right);
        }
    }

    /**
     * Merges two sorted subarrays into a single sorted array.
     *
     * @param arr   The original array containing the two sorted subarrays.
     * @param left  The starting index of the first subarray.
     * @param mid   The ending index of the first subarray (and mid + 1 is start of second).
     * @param right The ending index of the second subarray.
     */
    private static void merge(int[] arr, int left, int mid, int right) {
        // Calculate the sizes of the two subarrays to be merged.
        int n1 = mid - left + 1;
        int n2 = right - mid;

        // Create temporary arrays to hold the elements of the two subarrays.
        int[] L = new int[n1];
        int[] R = new int[n2];

        // Copy data from the original array into the temporary left array.
        for (int i = 0; i < n1; i++) {
            L[i] = arr[left + i];
        }
        // Copy data from the original array into the temporary right array.
        for (int i = 0; i < n2; i++) {
            R[i] = arr[mid + 1 + i];
        }

        // Initialize indices for the temporary arrays and the merged array.
        int i = 0; // Initial index of first subarray
        int j = 0; // Initial index of second subarray
        int k = left; // Initial index of merged subarray

        // Merge the temporary arrays back into the original array.
        // Compare elements from L and R, placing the smaller one into arr.
        while (i < n1 && j < n2) {
            if (L[i] <= R[j]) {
                arr[k] = L[i];
                i++;
            } else {
                arr[k] = R[j];
                j++;
            }
            k++;
        }

        // Copy any remaining elements of L[], if any.
        while (i < n1) {
            arr[k] = L[i];
            i++;
            k++;
        }

        // Copy any remaining elements of R[], if any.
        while (j < n2) {
            arr[k] = R[j];
            j++;
            k++;
        }
    }

    // Optional: A main method to test the merge sort implementation.
    public static void main(String[] args) {
        int[] data = {12, 11, 13, 5, 6, 7};
        System.out.println("Original Array:");
        printArray(data);

        mergeSort(data);

        System.out.println("\nSorted Array:");
        printArray(data);

        int[] data2 = {38, 27, 43, 3, 9, 82, 10};
        System.out.println("\nOriginal Array 2:");
        printArray(data2);

        mergeSort(data2);

        System.out.println("\nSorted Array 2:");
        printArray(data2);
    }

    // Helper method to print an array.
    private static void printArray(int[] arr) {
        for (int i = 0; i < arr.length; i++) {
            System.out.print(arr[i] + " ");
        }
        System.out.println();
    }
}