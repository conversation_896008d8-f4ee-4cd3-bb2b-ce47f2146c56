import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  CardMedia,
  Divider,
} from '@mui/material';
import KingBedOutlinedIcon from '@mui/icons-material/KingBedOutlined';
import BathtubOutlinedIcon from '@mui/icons-material/BathtubOutlined';
import SquareFootOutlinedIcon from '@mui/icons-material/SquareFootOutlined';

const properties = [
  {
    id: 1,
    address: '101 Pine Ave, Willow Creek',
    rent: 2200,
    bedrooms: 3,
    bathrooms: 2,
    status: 'available',
    squareFeet: 1500,
    imageUrl: 'https://images.unsplash.com/photo-1568605114967-8130f3a36994?auto=format&fit=crop&w=800',
  },
  {
    id: 2,
    address: '245 Maple St, Oasis Springs',
    rent: 1850,
    bedrooms: 2,
    bathrooms: 2,
    status: 'occupied',
    squareFeet: 1100,
    imageUrl: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?auto=format&fit=crop&w=800',
  },
  {
    id: 3,
    address: '789 Oak Dr, Newcrest',
    rent: 3500,
    bedrooms: 4,
    bathrooms: 3,
    status: 'available',
    squareFeet: 2400,
    imageUrl: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?auto=format&fit=crop&w=800',
  },
];

const StatusBadge = ({ status }: { status: string }) => (
  <Chip
    label={status}
    size="small"
    color={status === 'available' ? 'success' : 'warning'}
    sx={{ textTransform: 'capitalize', fontWeight: '600' }}
  />
);

const PropertyList: React.FC = () => {
  return (
    <Grid container spacing={4}>
      {properties.map((property) => (
        <Grid item xs={12} sm={6} md={4} key={property.id}>
          <Card sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
            <CardMedia
              component="img"
              height="200"
              image={property.imageUrl}
              alt={`Image of ${property.address}`}
            />
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Typography gutterBottom variant="h6" component="div" sx={{ fontWeight: 600, lineHeight: 1.3 }}>
                  {property.address}
                </Typography>
                <StatusBadge status={property.status} />
              </Box>
              <Typography variant="h5" color="primary" sx={{ my: 1, fontWeight: 'bold' }}>
                ${property.rent}<Typography component="span" color="text.secondary">/month</Typography>
              </Typography>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-around', color: 'text.secondary' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <KingBedOutlinedIcon sx={{ mr: 1 }} />
                  <Typography variant="body2">{property.bedrooms} Beds</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <BathtubOutlinedIcon sx={{ mr: 1 }} />
                  <Typography variant="body2">{property.bathrooms} Baths</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SquareFootOutlinedIcon sx={{ mr: 1 }} />
                  <Typography variant="body2">{property.squareFeet} sqft</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default PropertyList;
