/* Just a little extra comment here! */
body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
  background-color: #f0f0f0;
}
.custom-button {
  background-color: #2196F3; /* Blue */
  border: none;
  color: white;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  /* Button spacing */
  margin-top: 2px;
  margin-bottom: 2px;
  margin-left: 1px;
  margin-right: 1px;

  /* Style to indicate it's a clickable element */
  cursor: pointer;

  /* Rounded corners for a softer look */
  border-radius: 5px;
  
  /* Smooth transition for hover effects */
  transition: background-color 0.3s ease;
}

.custom-button:hover {
  background-color: #1976D2;
  transform: translateY(-1px);
}
.highlight-text {
  color: #FF5733;
  font-weight: bold;
}