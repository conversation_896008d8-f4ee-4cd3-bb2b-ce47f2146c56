"use client";

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/main-layout';
import PropertyList from '@/components/property/PropertyList';
import { Property } from '@/types';
import { apiService } from '@/services/api';
import { Plus } from 'lucide-react';

export default function PropertiesPage() {
  // This page manages all properties in the system.
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all'); // 'all', 'available', 'rented'
  const [sortOrder, setSortOrder] = useState<'none' | 'asc' | 'desc'>('none'); // 'none', 'asc', 'desc'
  const [sortOrder, setSortOrder] = useState<'none' | 'asc' | 'desc'>('none'); // 'none', 'asc', 'desc'

  useEffect(() => {
    const fetchProperties = async () => {
      setLoading(true);
      setError(null);
      try {
        const data = await apiService.getProperties();
        setProperties(data);
      } catch (e: any) {
        setError(e.message || "Failed to fetch properties");
        console.error("Properties fetch error:", e);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, []);

  // Optimized filter function to improve performance
  const filteredProperties = properties.filter(property => {
    const lowerAddress = property.address.toLowerCase();
    const lowerUnit = property.unit.toLowerCase();
    const matchesSearch = lowerAddress.includes(searchTerm.toLowerCase()) || lowerUnit.includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' ||
                            (statusFilter === 'available' && property.status === 'available') ||
                            (statusFilter === 'rented' && property.status === 'occupied');
    return matchesSearch && matchesStatus;
  });

  const sortedProperties = [...filteredProperties].sort((a, b) => {
    if (sortOrder === 'none') return 0;
    const nameA = a.address.toLowerCase();
    const nameB = b.address.toLowerCase();

    if (nameA < nameB) return sortOrder === 'asc' ? -1 : 1;
    if (nameA > nameB) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });

  const sortedProperties = [...filteredProperties].sort((a, b) => {
    if (sortOrder === 'none') return 0;
    const nameA = a.address.toLowerCase();
    const nameB = b.address.toLowerCase();

    if (nameA < nameB) return sortOrder === 'asc' ? -1 : 1;
    if (nameA > nameB) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Properties</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your property portfolio ({properties.length} total)
            </p>
          </div>
          <button className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
            <Plus className="h-4 w-4" />
            Add Property
          </button>
        </div>


        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
            <p className="text-red-700 dark:text-red-200">Error loading properties: {error}</p>
          </div>
        )}

        {/* Properties List */}
        {/* Search, Filter, and Sort */}
        <div className="flex flex-col sm:flex-row gap-4">
          <input
            type="text"
            placeholder="Search properties..."
            className="flex-1 p-2 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <select
            className="p-2 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="available">Available</option>
            <option value="rented">Rented</option>
          </select>
          <select
            className="p-2 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as 'none' | 'asc' | 'desc')}
          >
            <option value="none">Sort by Address</option>
            <option value="asc">Address (A-Z)</option>
            <option value="desc">Address (Z-A)</option>
          </select>
        </div>

        {/* Properties List */}
        {filteredProperties.length === 0 && (searchTerm || statusFilter !== 'all') ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-12 text-center">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No matching properties found</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try adjusting your search or filter criteria.
            </p>
          </div>
        ) : filteredProperties.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-12 text-center">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No properties added yet</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Get started by adding your first property.
            </p>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
              Add Property
            </button>
          </div>
        ) : (
          <PropertyList properties={sortedProperties} />
        )}
      </div>
    </MainLayout>
  );
}
