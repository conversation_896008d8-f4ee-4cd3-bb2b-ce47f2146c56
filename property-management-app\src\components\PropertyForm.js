import React, { useState } from 'react';
import {
  TextField,
  Button,
  Box,
  Typography,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';

function PropertyForm({ onAddProperty }) {
  const [address, setAddress] = useState('');
  const [rent, setRent] = useState('');
  const [bedrooms, setBedrooms] = useState('');
  const [bathrooms, setBathrooms] = useState('');
  const [status, setStatus] = useState('available'); // Default status
  const [squareFeet, setSquareFeet] = useState('');
  const [propertyType, setPropertyType] = useState(''); // New state for Property Type
  const [dateAvailable, setDateAvailable] = useState(''); // New state for Date Available

  const handleSubmit = (event) => {
    event.preventDefault();
    onAddProperty({
      address,
      rent: parseFloat(rent),
      bedrooms: parseInt(bedrooms),
      bathrooms: parseFloat(bathrooms),
      status,
      squareFeet: parseInt(squareFeet),
      propertyType, // Include new field
      dateAvailable, // Include new field
    });
    setAddress('');
    setRent('');
    setBedrooms('');
    setBathrooms('');
    setStatus('available');
    setSquareFeet('');
    setPropertyType(''); // Reset new field
    setDateAvailable(''); // Reset new field
  };

  return (
    <Box
      component="form"
      sx={{
        '& .MuiTextField-root': { m: 1, width: '25ch' },
        p: 2,
        border: '1px solid #ccc',
        borderRadius: '8px',
        backgroundColor: '#fff',
      }}
      noValidate
      autoComplete="off"
      onSubmit={handleSubmit}
    >
      <Typography variant="h5" gutterBottom>
        Add New Property
      </Typography>
      <TextField
        label="Address"
        variant="outlined"
        value={address}
        onChange={(e) => setAddress(e.target.value)}
        fullWidth
        required
      />
      <TextField
        label="Rent"
        variant="outlined"
        type="number"
        value={rent}
        onChange={(e) => setRent(e.target.value)}
        fullWidth
        required
      />
      <TextField
        label="Bedrooms"
        variant="outlined"
        type="number"
        value={bedrooms}
        onChange={(e) => setBedrooms(e.target.value)}
        fullWidth
        required
      />
      <TextField
        label="Bathrooms"
        variant="outlined"
        type="number"
        value={bathrooms}
        onChange={(e) => setBathrooms(e.target.value)}
        fullWidth
        required
      />
      <TextField
        label="Square Feet"
        variant="outlined"
        type="number"
        value={squareFeet}
        onChange={(e) => setSquareFeet(e.target.value)}
        fullWidth
        required
      />
      <TextField
        label="Date Available"
        variant="outlined"
        type="date"
        value={dateAvailable}
        onChange={(e) => setDateAvailable(e.target.value)}
        fullWidth
        InputLabelProps={{
          shrink: true,
        }}
      />
      <FormControl fullWidth sx={{ m: 1 }}>
        <InputLabel id="property-type-select-label">Property Type</InputLabel>
        <Select
          labelId="property-type-select-label"
          id="property-type-select"
          value={propertyType}
          label="Property Type"
          onChange={(e) => setPropertyType(e.target.value)}
          required
        >
          <MenuItem value="Apartment">Apartment</MenuItem>
          <MenuItem value="House">House</MenuItem>
          <MenuItem value="Condo">Condo</MenuItem>
        </Select>
      </FormControl>
      <FormControl fullWidth sx={{ m: 1 }}>
        <InputLabel id="status-select-label">Status</InputLabel>
        <Select
          labelId="status-select-label"
          id="status-select"
          value={status}
          label="Status"
          onChange={(e) => setStatus(e.target.value)}
        >
          <MenuItem value="available">Available</MenuItem>
          <MenuItem value="occupied">Occupied</MenuItem>
        </Select>
      </FormControl>
      <Button type="submit" variant="contained" sx={{ mt: 2 }}>
        Add Property
      </Button>
    </Box>
  );
}

export default PropertyForm;