import React, { useState, useMemo } from 'react';
import PropertyCard from './PropertyCard';
import { Property } from '@/types';

interface PropertyListProps {
  properties: Property[];
}

export default function PropertyList({ properties }: PropertyListProps) {
  const [filterType, setFilterType] = useState<string>('All');
  const [sortOrder, setSortOrder] = useState<string>('None'); // 'None', 'PriceAsc', 'PriceDesc'
  const [filterBathrooms, setFilterBathrooms] = useState<string>('All');
  const [filterBathrooms, setFilterBathrooms] = useState<string>('All');
  const [filterBathrooms, setFilterBathrooms] = useState<string>('All');
  const [filterBathrooms, setFilterBathrooms] = useState<string>('All');

  const filteredAndSortedProperties = useMemo(() => {
    let currentProperties = [...properties];

    // Filtering logic
    if (filterType !== 'All') {
      currentProperties = currentProperties.filter(
        (property) => property.type === filterType
      );
    }

    // Filtering by bathrooms logic
    if (filterBathrooms !== 'All') {
      currentProperties = currentProperties.filter(
        (property) => property.bathrooms >= parseInt(filterBathrooms)
      );
    }

    // Filtering by bathrooms logic
    if (filterBathrooms !== 'All') {
      currentProperties = currentProperties.filter(
        (property) => property.bathrooms >= parseInt(filterBathrooms)
      );
    }

    // Filtering by bathrooms logic
    if (filterBathrooms !== 'All') {
      currentProperties = currentProperties.filter(
        (property) => property.bathrooms >= parseInt(filterBathrooms)
      );
    }

    // Filtering by bathrooms logic
    if (filterBathrooms !== 'All') {
      currentProperties = currentProperties.filter(
        (property) => property.bathrooms >= parseInt(filterBathrooms)
      );
    }

    // Sorting logic
    if (sortOrder === 'PriceAsc') {
      currentProperties.sort((a, b) => a.rent - b.rent);
    } else if (sortOrder === 'PriceDesc') {
      currentProperties.sort((a, b) => b.rent - a.rent);
    }

    return currentProperties;
  }, [properties, filterType, sortOrder, filterBathrooms]);

  return (
    <div>
      <div className="mb-6 flex space-x-4">
        {/* Filter by Type */}
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="p-2 border rounded"
        >
          <option value="All">All Types</option>
          <option value="Apartment">Apartment</option>
          <option value="House">House</option>
          <option value="Condo">Condo</option>
        </select>

        {/* Sort by Price */}
        <select
          value={sortOrder}
          onChange={(e) => setSortOrder(e.target.value)}
          className="p-2 border rounded"
        >
          <option value="None">Sort By</option>
          <option value="PriceAsc">Price: Low to High</option>
          <option value="PriceDesc">Price: High to Low</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAndSortedProperties.map((property) => (
          <PropertyCard key={property.id} property={property} />
        ))}
      </div>
    </div>
  );
}
