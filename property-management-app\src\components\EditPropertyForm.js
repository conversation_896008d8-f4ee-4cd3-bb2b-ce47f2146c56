import React, { useState } from 'react';
import PropTypes from 'prop-types';

const EditPropertyForm = ({ property, onSave, onCancel }) => {
  const [address, setAddress] = useState(property.address);
  const [rent, setRent] = useState(property.rent);
  const [bedrooms, setBedrooms] = useState(property.bedrooms);
  const [bathrooms, setBathrooms] = useState(property.bathrooms);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave({ address, rent, bedrooms, bathrooms });
  };

  return (
    <form className="add-property-form" onSubmit={handleSubmit}>
      <input
        type="text"
        value={address}
        onChange={(e) => setAddress(e.target.value)}
        placeholder="Address"
      />
      <input
        type="number"
        value={rent}
        onChange={(e) => setRent(parseInt(e.target.value))}
        placeholder="Rent"
      />
      <input
        type="number"
        value={bedrooms}
        onChange={(e) => setBedrooms(parseInt(e.target.value))}
        placeholder="Bedrooms"
      />
      <input
        type="number"
        value={bathrooms}
        onChange={(e) => setBathrooms(parseInt(e.target.value))}
        placeholder="Bathrooms"
      />
      <button type="submit">Save</button>
      <button type="button" onClick={onCancel}>Cancel</button>
    </form>
  );
};

EditPropertyForm.propTypes = {
  property: PropTypes.object.isRequired,
  onSave: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
};

export default EditPropertyForm;