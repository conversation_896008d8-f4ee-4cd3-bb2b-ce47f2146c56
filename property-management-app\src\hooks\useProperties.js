import { useState, useCallback, useMemo } from 'react';

const useProperties = (initialProperties) => {
  const [properties, setProperties] = useState(initialProperties);
  const [propertyCount, setPropertyCount] = useState(initialProperties.length);
  const [selectedProperty, setSelectedProperty] = useState(initialProperties[0]);
  const [editingPropertyId, setEditingPropertyId] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [minRent, setMinRent] = useState('');
  const [maxRent, setMaxRent] = useState('');

  const applyFilters = useCallback((props, currentSearchTerm, currentMinRent, currentMaxRent) => {
    return props.filter((property) => {
      const addressMatch = property.address.toLowerCase().includes(currentSearchTerm.toLowerCase());
      const rentInRange =
        (currentMinRent === '' || property.rent >= parseInt(currentMinRent)) &&
        (currentMaxRent === '' || property.rent <= parseInt(currentMaxRent));
      return addressMatch && rentInRange;
    });
  }, []);

  const filteredProperties = useMemo(() => {
    return applyFilters(properties, searchTerm, minRent, maxRent);
  }, [properties, searchTerm, minRent, maxRent, applyFilters]);

  const handleSearch = useCallback((newSearchTerm) => {
    setSearchTerm(newSearchTerm);
  }, []);

  const handleMinRentChange = useCallback((newMinRent) => {
    setMinRent(newMinRent);
  }, []);

  const handleMaxRentChange = useCallback((newMaxRent) => {
    setMaxRent(newMaxRent);
  }, []);

  const handlePropertySelect = useCallback((propertyId) => {
    const property = filteredProperties.find((prop) => prop.id === propertyId);
    setSelectedProperty(property);
  }, [filteredProperties]);

  const handleAddProperty = useCallback((newProperty) => {
    const newId = propertyCount + 1;
    setProperties((prevProperties) => [...prevProperties, { ...newProperty, id: newId }]);
    setPropertyCount(newId);
  }, [propertyCount]);

  const handleEditProperty = useCallback((propertyId) => {
    setEditingPropertyId(propertyId);
  }, []);

  const handleSaveProperty = useCallback((propertyId, updatedProperty) => {
    setProperties((prevProperties) => {
      const updatedProps = prevProperties.map((property) =>
        property.id === propertyId ? { ...property, ...updatedProperty } : property
      );
      const updatedSelectedProp = updatedProps.find((property) => property.id === propertyId);
      setSelectedProperty(updatedSelectedProp);
      return updatedProps;
    });
    setEditingPropertyId(null);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setEditingPropertyId(null);
  }, []);

  const handleDeleteProperty = useCallback((propertyId) => {
    setProperties((prevProperties) => {
      const updatedProps = prevProperties.filter((property) => property.id !== propertyId);
      if (selectedProperty && selectedProperty.id === propertyId) {
        setSelectedProperty(updatedProps.length > 0 ? updatedProps[0] : null);
      }
      return updatedProps;
    });
  }, [selectedProperty]);

  return {
    searchTerm,
    minRent,
    maxRent,
    propertyCount,
    properties,
    selectedProperty,
    editingPropertyId,
    filteredProperties,
    handleSearch,
    handleMinRentChange,
    handleMaxRentChange,
    handlePropertySelect,
    handleAddProperty,
    handleEditProperty,
    handleSaveProperty,
    handleCancelEdit,
    handleDeleteProperty,
  };
};

export default useProperties;