def factorial(n):
    """
    This function calculates the factorial of a non-negative integer.
    """
    if n == 0:
        return 1
    else:
        return n * factorial(n-1)

if __name__ == "__main__":
    num = 5
    print(f"The factorial of {num} is {factorial(num)}")

    num = 0
    print(f"The factorial of {num} is {factorial(num)}")

    num = 7
    print(f"The factorial of {num} is {factorial(num)}")