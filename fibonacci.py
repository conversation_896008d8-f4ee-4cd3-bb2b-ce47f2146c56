def fibon<PERSON>ci(n):
    """
    This function generates the nth Fi<PERSON><PERSON><PERSON> number.
    """
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b

if __name__ == "__main__":
    print(f"<PERSON>bon<PERSON><PERSON>(0): {fi<PERSON><PERSON><PERSON>(0)}")
    print(f"Fibonacci(1): {fi<PERSON><PERSON><PERSON>(1)}")
    print(f"Fibonac<PERSON>(2): {fi<PERSON><PERSON><PERSON>(2)}")
    print(f"Fi<PERSON><PERSON><PERSON>(5): {fibon<PERSON><PERSON>(5)}")
    print(f"Fi<PERSON><PERSON><PERSON>(10): {fi<PERSON><PERSON><PERSON>(10)}")