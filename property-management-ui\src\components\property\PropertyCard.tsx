import React, { useEffect, useState } from 'react';
import { Building2, Bed, Bath, Square, MapPin, Calendar, Edit, Trash2, Users, Wifi, Car, Tv, Snowflake } from 'lucide-react';
import { Property, Tenant } from '@/types';
import { apiService } from '@/services/api';

interface PropertyCardProps {
  property: Property;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'available':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'occupied':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'maintenance':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'house':
      return '🏠';
    case 'apartment':
      return '🏢';
    case 'condo':
      return '🏘️';
    case 'townhouse':
      return '🏘️';
    default:
      return '🏠';
  }
};

// Function to get appropriate icon for amenities
const getAmenityIcon = (amenity: string) => {
  const amenityLower = amenity.toLowerCase();
  if (amenityLower.includes('wifi') || amenityLower.includes('internet')) return <Wifi className="h-3 w-3 mr-1" />;
  if (amenityLower.includes('parking') || amenityLower.includes('garage')) return <Car className="h-3 w-3 mr-1" />;
  if (amenityLower.includes('tv') || amenityLower.includes('cable')) return <Tv className="h-3 w-3 mr-1" />;
  if (amenityLower.includes('ac') || amenityLower.includes('air') || amenityLower.includes('conditioning')) return <Snowflake className="h-3 w-3 mr-1" />;
  return null;
};
export default function PropertyCard({ property }: PropertyCardProps) {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [errorTenants, setErrorTenants] = useState<string | null>(null);

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        setLoadingTenants(true);
        const data = await apiService.getTenantByPropertyId(Number(property.id));
        setTenants(data);
      } catch (err) {
        setErrorTenants('Failed to load tenants');
        console.error('Failed to load tenants:', err);
      } finally {
        setLoadingTenants(false);
      }
    };

    fetchTenants();
  }, [property.id]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg transform hover:scale-110 transition-transform duration-200">{getTypeIcon(property.type || 'house')}</span>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {property.title || `Property at ${property.address.split(',')[0]}`}
              </h3>
            </div>
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <MapPin className="h-3 w-3 mr-1" />
              <span className="truncate">{property.address}</span>
            </div>
          </div>
          <div className="flex gap-1 ml-2">
            <button
              className="p-1 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-all duration-200"
              title="Edit property"
            >
              <Edit className="h-4 w-4" />
            </button>
            <button
              className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-all duration-200"
              title="Delete property"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Property Details */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Bed className="h-4 w-4 mr-1" />
            <span>{property.bedrooms} bed</span>
          </div>
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Bath className="h-4 w-4 mr-1" />
            <span>{property.bathrooms} bath</span>
          </div>
          {property.squareFeet && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <Square className="h-4 w-4 mr-1" />
              <span>{property.squareFeet} sq ft</span>
            </div>
          )}
        </div>

        {/* Description */}
        {property.description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
            {property.description}
          </p>
        )}

        {/* Amenities */}
        {property.amenities && property.amenities.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {property.amenities.slice(0, 3).map((amenity, index) => (
                <span
                  key={index}
                  className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded flex items-center"
                >
                  {getAmenityIcon(amenity)}
                  {amenity.replace(/_/g, ' ')}
                </span>
              ))}
              {property.amenities.length > 3 && (
                <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded">
                  +{property.amenities.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Tenants Section */}
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-200 mb-2 flex items-center">
            <Users className="h-4 w-4 mr-1" /> Current Tenants
          </h4>
          {loadingTenants ? (
            <p className="text-sm text-gray-500 dark:text-gray-400">Loading tenants...</p>
          ) : errorTenants ? (
            <p className="text-sm text-red-500 dark:text-red-400">{errorTenants}</p>
          ) : tenants.length > 0 ? (
            <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400">
              {tenants.map((tenant) => (
                <li key={tenant.id}>{tenant.name}</li>
              ))}
            </ul>
          ) : (
            <p className="text-sm text-gray-500 dark:text-gray-400">No tenants currently assigned.</p>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              ${property.rent.toLocaleString()}
              <span className="text-sm font-normal text-gray-600 dark:text-gray-400">/month</span>
            </p>
            {property.lastUpdated && (
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                <Calendar className="h-3 w-3 mr-1" />
                <span>Updated {new Date(property.lastUpdated).toLocaleDateString()}</span>
              </div>
            )}
          </div>
          <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(property.status)}`}>
            {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
          </span>
        </div>
      </div>
    </div>
  );
}
