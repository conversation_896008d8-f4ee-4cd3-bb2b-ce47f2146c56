import React from 'react';

const ProfilePage = () => {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">User Profile</h1>

      {/* Basic Info Section */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-gray-600">Name:</p>
            <p className="font-medium"><PERSON></p> {/* Placeholder */}
          </div>
          <div>
            <p className="text-gray-600">Email:</p>
            <p className="font-medium"><EMAIL></p> {/* Placeholder */}
          </div>
          <div>
            <p className="text-gray-600">Member Since:</p>
            <p className="font-medium">January 2023</p> {/* Placeholder */}
          </div>
        </div>
      </div>

      {/* Contact Info Section */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Contact Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-gray-600">Phone:</p>
            <p className="font-medium">(*************</p> {/* Placeholder */}
          </div>
          <div>
            <p className="text-gray-600">Address:</p>
            <p className="font-medium">123 Main St, Anytown, USA</p> {/* Placeholder */}
          </div>
        </div>
      </div>

      {/* Settings Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Settings</h2>
        {/* Add settings options here */}
        <p className="text-gray-600">Notification Preferences</p> {/* Placeholder */}
        <p className="text-gray-600">Privacy Settings</p> {/* Placeholder */}
      </div>
    </div>
  );
};

export default ProfilePage;