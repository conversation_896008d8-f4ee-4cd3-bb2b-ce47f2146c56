{"name": "my-app", "version": "1.2.1", "description": "A more complex app with updated dependencies and a new feature", "main": "main.js", "scripts": {"start": "cross-env react-scripts start", "test": "jest", "lint": "eslint .", "build": "webpack"}, "dependencies": {"axios": "^1.9.0", "body-parser": "^2.2.0", "clsx": "^2.1.1", "cors": "^2.8.5", "express": "^4.21.2", "helmet": "^8.1.0", "lodash": "^4.17.21", "moment": "^2.29.1", "mongoose": "^8.15.1", "morgan": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"cross-env": "^7.0.3", "eslint": "^7.0.0", "jest": "^29.0.0", "prettier": "^2.8.0"}, "randomEditKey": "randomValue", "anotherRandomEdit": true, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}