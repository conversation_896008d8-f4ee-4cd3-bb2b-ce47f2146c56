import React from 'react';
import { AppBar, Toolbar, Typography, Button, Box, SvgIcon } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import BusinessIcon from '@mui/icons-material/Business';

const Navbar: React.FC = () => {
  return (
    <AppBar position="static" color="transparent" elevation={0} sx={{ borderBottom: '1px solid #e0e0e0' }}>
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none', color: 'inherit' }} component={RouterLink} to="/">
          <SvgIcon component={BusinessIcon} sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" noWrap>
            PropManager
          </Typography>
        </Box>
        <Box>
          <Button component={RouterLink} to="/" sx={{ fontWeight: 600 }}>
            Dashboard
          </Button>
          <Button component={RouterLink} to="/properties" sx={{ fontWeight: 600 }}>
            Properties
          </Button>
          <Button variant="contained" component={RouterLink} to="/add-property" sx={{ ml: 2 }}>
            Add Property
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
