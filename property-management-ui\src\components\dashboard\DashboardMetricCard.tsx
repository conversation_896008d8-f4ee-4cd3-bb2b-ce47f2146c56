import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { LucideIcon, TrendingUp, TrendingDown } from "lucide-react";

interface DashboardMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  iconColor: string;
  trend?: string;
}

const DashboardMetricCard: React.FC<DashboardMetricCardProps> = ({
  title,
  value,
  icon: Icon,
  iconColor,
  trend,
}) => {
  const isPositiveTrend = trend?.startsWith("+");
  const trendColor = isPositiveTrend ? "text-green-500" : "text-red-500";
  const TrendIcon = isPositiveTrend ? TrendingUp : TrendingDown;

  return (
    <Card className="overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</CardTitle>
        <div className={`p-2 rounded-full ${iconColor.replace('text', 'bg')} bg-opacity-10`}>
          <Icon className={`h-4 w-4 ${iconColor}`} />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900 dark:text-white">{value}</div>
        {trend && (
          <div className="flex items-center mt-2">
            <TrendIcon className={`h-4 w-4 ${trendColor} mr-1`} />
            <span className={`text-sm font-medium ${trendColor}`}>{trend}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DashboardMetricCard;