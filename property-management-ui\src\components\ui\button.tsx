// This is a random edit to the button component.
// Added a new prop to enable a pulsing animation effect.
// The button now has a 'pulse' class that can be applied to make it pulse.
import * as React from "react"

interface ButtonProps extends React.HTMLAttributes<HTMLButtonElement> {
  className?: string;
  children?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'info';
  isBold?: boolean;
  isRounded?: boolean;
  isWide?: boolean;
  isSmallText?: boolean;
  isPulsing?: boolean;
  isLoading?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, children, variant = 'primary', isBold, isRounded, isWide, isSmallText, isPulsing, size = 'medium', ...props }, ref) => {
    const variantStyles = {
      primary: 'bg-purple-600 text-white hover:bg-purple-700',
      secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300',
      danger: 'bg-red-600 text-white hover:bg-red-700',
      success: 'bg-green-600 text-white hover:bg-green-700',
      warning: 'bg-orange-500 text-white hover:bg-orange-600',
      info: 'bg-blue-500 text-white hover:bg-blue-600'
    };

    const customStyles = [
      isBold ? 'font-bold' : '',
      isRounded ? 'rounded-full' : '',
      isWide ? 'px-8' : '',
      isSmallText ? 'text-sm' : '',
      isPulsing ? 'animate-pulse' : '', // New pulse animation class
      size === 'small' ? 'px-2 py-1 text-sm' : '',
      size === 'large' ? 'px-6 py-3 text-lg' : ''
    ].filter(Boolean).join(' ');

    return (
      <button
        className={`px-4 py-2 rounded ${variantStyles[variant]} transition-all duration-300 transform hover:scale-105 shadow-md ${customStyles} ${className}`}
        ref={ref}
        {...props}
      >
        {children}
        {/* Random edit */}
      </button>
    );
  }
);
Button.displayName = "Button"

export { Button }
// Added a new comment at the end