import React from 'react';

function PriceRangeFilter({ onMinRentChange, onMaxRentChange }) {
  return (
    <div className="price-range-filter">
      <label>Min Rent:</label>
      <input
        type="number"
        placeholder="Min"
        onChange={(e) => onMinRentChange(e.target.value)}
      />
      <label>Max Rent:</label>
      <input
        type="number"
        placeholder="Max"
        onChange={(e) => onMaxRentChange(e.target.value)}
      />
    </div>
  );
}

export default PriceRangeFilter;