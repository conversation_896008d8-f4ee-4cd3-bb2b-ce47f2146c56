def is_palindrome(s):
    """
    Checks if a given string is a palindrome, ignoring cases and spaces.

    Args:
        s (str): The input string.

    Returns:
        bool: True if the string is a palindrome, False otherwise.
    """
    # Remove spaces and convert to lowercase
    cleaned_s = "".join(char.lower() for char in s if char.isalnum())

    # Compare the cleaned string with its reverse
    return cleaned_s == cleaned_s[::-1]

if __name__ == "__main__":
    # Test cases
    print(f"'Racecar' is a palindrome: {is_palindrome('Racecar')}")
    print(f"'A man a plan a canal Panama' is a palindrome: {is_palindrome('A man a plan a canal Panama')}")
    print(f"'Hello' is a palindrome: {is_palindrome('Hello')}")
    print(f"'No lemon, no melon' is a palindrome: {is_palindrome('No lemon, no melon')}")
    print(f"'Was it a car or a cat I saw' is a palindrome: {is_palindrome('Was it a car or a cat I saw')}")
    print(f"'Python' is a palindrome: {is_palindrome('Python')}")