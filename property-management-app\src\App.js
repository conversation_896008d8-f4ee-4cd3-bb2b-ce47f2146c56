import React from 'react'; // This is a test comment
import './App.css';
import initialProperties from './data/initialProperties';
import PropertyList from './components/PropertyList';
import Search from './components/Search';
import PriceRangeFilter from './components/PriceRangeFilter';
import PropertyDetails from './components/PropertyDetails';
import PropertyForm from './components/PropertyForm';
import EditPropertyForm from './components/EditPropertyForm';
import useProperties from './hooks/useProperties';


const App = () => { // This is our main application component
  const {
    searchTerm,
    minRent,
    maxRent,
    propertyCount,
    properties,
    selectedProperty,
    editingPropertyId,
    filteredProperties,
    handleSearch,
    handleMinRentChange,
    handleMaxRentChange,
    handlePropertySelect,
    handleAddProperty,
    handleEditProperty,
    handleSaveProperty,
    handleCancelEdit,
    handleDeleteProperty,
  } = useProperties(initialProperties);

console.log('App component rendering'); // Log to confirm component re-renders
  return (
    <div className="app-container">
      <h1>Qapt's Super Property Manager!</h1>
      <Search onSearch={handleSearch} />
      <PriceRangeFilter
        onMinRentChange={handleMinRentChange}
        onMaxRentChange={handleMaxRentChange}
      />
      <PropertyForm onAddProperty={handleAddProperty} />
      {filteredProperties.length === 0 ? (
        <div className="no-properties">No properties found.</div>
      ) : (
        <PropertyList
          properties={filteredProperties}
          onPropertySelect={handlePropertySelect}
          onEditProperty={handleEditProperty}
          onDeleteProperty={handleDeleteProperty}
        />
      )}
      <PropertyDetails property={selectedProperty} />
      {editingPropertyId && (
        <EditPropertyForm
          property={properties.find((property) => property.id === editingPropertyId)}
          onSave={(updated) => handleSaveProperty(editingPropertyId, updated)}
          onCancel={handleCancelEdit}
        />
      )}
      <p>Total properties available: {propertyCount}</p>
    </div>
  );
};

export default App;