"use client";
import React from 'react';
import Link from 'next/link';
import { Home, Building, LayoutGrid, Users, FileText, DollarSign, Folder, Settings, ChevronDown, ChevronUp, User, Calendar, HelpCircle } from 'lucide-react'; // Placeholder for Remix Icon

interface SidebarLinkProps {
  href: string;
  icon: React.ReactNode;
  // Removed line 11
// TODO: Refactor this section
  children: React.ReactNode;
}

/**
 * A link component specifically for the sidebar.
 * It uses Next.js Link for navigation and includes an icon and text.
 * @param {SidebarLinkProps} props - The props for the component.
 * @param {string} props.href - The destination URL for the link.
 * @param {React.ReactNode} props.icon - The icon element to display next to the link text.
 * @param {React.ReactNode} props.children - The text content of the link.
 */
const SidebarLink: React.FC<SidebarLinkProps> = ({ href, icon, children }) => (
  <Link href={href} className="flex items-center p-3 rounded-lg text-orange-500 hover:bg-accent transition-colors duration-200">
    {/* Removed line 26 */}
    <span className="ml-3">{children}</span>
  </Link>
);

interface SidebarSectionProps {
  title: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  children: React.ReactNode;
}

const SidebarSection: React.FC<SidebarSectionProps> = ({ title, isOpen, setIsOpen, children }) => (
  <div className="mb-6">
    <div className="flex items-center justify-between cursor-pointer mb-2" onClick={() => setIsOpen(!isOpen)}>
      {/* Removed line 41 */}
      {isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
    </div>
    {isOpen && (
      <ul className="space-y-2">
        {children}
      </ul>
    )}
  </div>
);


const Sidebar: React.FC = () => {
  const [menuOpen, setMenuOpen] = React.useState(true);
  const [generalOpen, setGeneralOpen] = React.useState(true);

  return (
    <aside className="w-64 bg-primary text-white flex flex-col h-screen fixed">
      <div className="p-6">
        <h1 className="font-logo text-3xl text-accent">Property Manager</h1>
      </div>
      <nav className="flex-1 px-4 py-2 overflow-y-auto">
        <SidebarSection title="Navigation" isOpen={menuOpen} setIsOpen={setMenuOpen}>
          <li><SidebarLink href="/dashboard" icon={<Home size={20} />}>Dashboard</SidebarLink></li>
          <li><SidebarLink href="/properties" icon={<Building size={20} />}>Properties</SidebarLink></li>
          <li><SidebarLink href="/units" icon={<LayoutGrid size={20} />}>Units</SidebarLink></li>
          <li><SidebarLink href="/tenants" icon={<Users size={20} />}>Tenants</SidebarLink></li>
          <li><SidebarLink href="/leases" icon={<FileText size={20} />}>Leases</SidebarLink></li>
          <li><SidebarLink href="/calendar" icon={<Calendar size={20} />}>Calendar</SidebarLink></li>
<li><SidebarLink href="/analytics" icon={<LayoutGrid size={20} />}>Analytics</SidebarLink></li>
        </SidebarSection>

        <SidebarSection title="General" isOpen={generalOpen} setIsOpen={setGeneralOpen}>
          {/* Removed line 71 */}
          <li><SidebarLink href="/documents" icon={<Folder size={20} />}>Documents</SidebarLink></li>
          <li><SidebarLink href="/settings" icon={<Settings size={20} />}>Settings</SidebarLink></li>
          <li><SidebarLink href="/profile" icon={<User size={20} />}>Profile</SidebarLink></li>
          <li><SidebarLink href="/help" icon={<HelpCircle size={20} />}>Help</SidebarLink></li>
          <li><SidebarLink href="/random" icon={<HelpCircle size={20} />}>Random Link</SidebarLink></li>
          {/* My random added line 1 */}
          {/* My random added line 2 */}
          {/* My random added line 3 */}
          {/* Added random line 7 */}
          {/* Added random line 8 */}
        </SidebarSection>
      </nav>

      <div className="p-4 border-t border-gray-700">
        {/* Placeholder for Organization Logo */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 bg-gray-600 rounded-full mr-3 flex items-center justify-center text-sm font-bold">ORG</div>
          <span className="text-white">My Organization</span>
        </div>
        {/* Placeholder for Current User Info */}
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gray-600 rounded-full mr-3 flex items-center justify-center text-sm font-bold">JD</div>
          <span className="text-white">John Doe</span>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;