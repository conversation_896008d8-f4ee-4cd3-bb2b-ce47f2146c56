export interface Tenant {
  id: number;
  name: string;
  email: string;
  phone: string;
  propertyId: number;
  leaseStartDate: Date;
  leaseEndDate: Date;
  rentAmount: number;
  status: 'active' | 'inactive' | 'evicted';
}

export interface TenantFormData {
  name: string;
  email: string;
  phone: string;
  propertyId: number;
  leaseStartDate: Date;
  leaseEndDate: Date;
  rentAmount: number;
  status: 'active' | 'inactive' | 'evicted';
}