export interface DashboardStats {
  totalProperties: number;
  occupiedProperties: number;
  availableProperties: number;
  totalTenants: number;
  activeLeases: number;
  occupancyRate: number;
  totalRent: number;
  averageRent: number;
  totalPropertiesTrend?: string;
  occupancyRateTrend?: string;
  totalTenantsTrend?: string;
  totalRentTrend?: string;
}

export interface Property {
  id: string;
  address: string;
  unit: string;
  rent: number;
  status: 'occupied' | 'available' | 'maintenance';
  imageUrl: string;
  type: 'house' | 'apartment' | 'condo' | 'townhouse';
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  description: string;
  amenities: string[];
  lastUpdated: string;
}

export interface Tenant {
  id: string;
  name: string;
  email: string;
  phone: string;
  property: string;
  leaseEndDate: string;
}

export interface Lease {
  id: string;
  propertyId: string;
  tenantId: string;
  startDate: string;
  endDate: string;
  rentAmount: number;
}

export interface PropertyFormData {
  address: string;
  unit: string;
  rent: number;
  status: 'occupied' | 'available' | 'maintenance';
  imageUrl: string;
  type: 'house' | 'apartment' | 'condo' | 'townhouse';
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  description: string;
  amenities: string[];
  lastUpdated: string;
}

export interface TenantFormData {
  name: string;
  email: string;
  phone: string;
  property: string;
  leaseEndDate: string;
}

export interface LeaseFormData {
  propertyId: string;
  tenantId: string;
  startDate: string;
  endDate: string;
  rentAmount: number;
}
export interface MaintenanceRequest {
  id: string;
  propertyId: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed';
  priority: 'high' | 'medium' | 'low';
  requestedDate: string;
}