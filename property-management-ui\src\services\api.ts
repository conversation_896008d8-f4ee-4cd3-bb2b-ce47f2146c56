import { Property, Tenant, <PERSON>se, DashboardStats, PropertyFormData, TenantFormData, LeaseFormData, MaintenanceRequest } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  private async createResource<T, F>(endpoint: string, data: F): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Properties API
  async getProperties(): Promise<Property[]> {
    return this.request<Property[]>('/properties');
  }

  async getProperty(id: number): Promise<Property> {
    return this.request<Property>(`/properties/${id}`);
  }

  async createProperty(data: PropertyFormData): Promise<Property> {
    return this.createResource<Property, PropertyFormData>('/properties', data);
  }

  async updateProperty(id: number, data: Partial<PropertyFormData>): Promise<Property> {
    return this.request<Property>(`/properties/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteProperty(id: number): Promise<void> {
    return this.request<void>(`/properties/${id}`, {
      method: 'DELETE',
    });
  }

  // Tenants API
  async getTenants(): Promise<Tenant[]> {
    return this.request<Tenant[]>('/tenants');
  }

  async getTenant(id: number): Promise<Tenant> {
    return this.request<Tenant>(`/tenants/${id}`);
  }

  async createTenant(data: TenantFormData): Promise<Tenant> {
    return this.createResource<Tenant, TenantFormData>('/tenants', data);
  }

async getTenantByPropertyId(propertyId: number): Promise<Tenant[]> {
    return this.request<Tenant[]>(`/tenants/property/${propertyId}`);
  }
  async updateTenant(id: number, data: Partial<TenantFormData>): Promise<Tenant> {
    return this.request<Tenant>(`/tenants/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteTenant(id: number): Promise<void> {
    return this.request<void>(`/tenants/${id}`, {
      method: 'DELETE',
    });
  }

  // Leases API
  async getLeases(): Promise<Lease[]> {
    return this.request<Lease[]>('/leases');
  }

  async getLease(id: number): Promise<Lease> {
    return this.request<Lease>(`/leases/${id}`);
  }

  async createLease(data: LeaseFormData): Promise<Lease> {
    return this.createResource<Lease, LeaseFormData>('/leases', data);
  }

  async updateLease(id: number, data: Partial<LeaseFormData>): Promise<Lease> {
    return this.request<Lease>(`/leases/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteLease(id: number): Promise<void> {
    return this.request<void>(`/leases/${id}`, {
      method: 'DELETE',
    });
  }

  async getLeasesByPropertyId(propertyId: number): Promise<Lease[]> {
    return this.request<Lease[]>(`/leases/property/${propertyId}`);
  }

  async getLeasesByTenantId(tenantId: number): Promise<Lease[]> {
    return this.request<Lease[]>(`/leases/tenant/${tenantId}`);
  }

  // Dashboard API
  async getDashboardStats(): Promise<DashboardStats> {
    // In a real application, this would fetch data from a backend API.
    // For demonstration, we'll return mock data with trend values.
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          totalProperties: 120,
          occupiedProperties: 95,
          availableProperties: 25,
          totalTenants: 250,
          activeLeases: 90,
          occupancyRate: (95 / 120) * 100,
          totalRent: 150000,
          averageRent: 1250,
          totalPropertiesTrend: "****%",
          occupancyRateTrend: "+1.2%",
          totalTenantsTrend: "+3.8%",
          totalRentTrend: "+5.2%",
        });
      }, 1000); // Simulate network delay
    });
  }

  // Maintenance Requests API
  async getMaintenanceRequests(): Promise<MaintenanceRequest[]> {
    return this.request<MaintenanceRequest[]>('/maintenance-requests');
  }

  async getMaintenanceRequest(id: string): Promise<MaintenanceRequest> {
    return this.request<MaintenanceRequest>(`/maintenance-requests/${id}`);
  }

  async createMaintenanceRequest(data: Omit<MaintenanceRequest, 'id'>): Promise<MaintenanceRequest> {
    return this.createResource<MaintenanceRequest, Omit<MaintenanceRequest, 'id'>>('/maintenance-requests', data);
  }

  async updateMaintenanceRequest(id: string, data: Partial<MaintenanceRequest>): Promise<MaintenanceRequest> {
    return this.request<MaintenanceRequest>(`/maintenance-requests/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteMaintenanceRequest(id: string): Promise<void> {
    return this.request<void>(`/maintenance-requests/${id}`, {
      method: 'DELETE',
    });
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request<{ status: string; timestamp: string }>('/health');
  }
}

export const apiService = new ApiService();
