# This script contains several utility functions for mathematical operations.

def fibonacci(n):
    """
    Calculates the nth Fibonacci number.
    The Fibonacci sequence is a series of numbers where each number is the sum of the two preceding ones,
    usually starting with 0 and 1.
    """
    a, b = 0, 1
    for i in range(n):
        yield a
        a, b = b, a + b

def is_prime(num):
    """
    Checks if a given number is a prime number.
    A prime number is a natural number greater than 1 that has no positive divisors other than 1 and itself.
    """
    if num <= 1:
        return False
    for i in range(2, int(num**0.5) + 1):
        if num % i == 0:
            return False
    return True

def calculate_sum_of_squares(numbers):
    """
    Calculates the sum of squares for a list of numbers.
    """
    total = 0
    for number in numbers:
        total += number * number
    return total

# Main execution block to demonstrate the functions
if __name__ == "__main__":
    print("--- Fibonacci Sequence ---")
    fib_limit = 10
    fib_series = list(fibonacci(fib_limit))
    print(f"First {fib_limit} Fibonacci numbers: {fib_series}")

    print("\n--- Prime Number Check ---")
    numbers_to_check = [1, 2, 7, 10, 13, 29, 30]
    for num in numbers_to_check:
        if is_prime(num):
            print(f"{num} is a prime number.")
        else:
            print(f"{num} is not a prime number.")

    print("\n--- Sum of Squares ---")
    my_numbers = [1, 2, 3, 4, 5]
    sum_sq = calculate_sum_of_squares(my_numbers)
    print(f"The sum of squares for {my_numbers} is: {sum_sq}")

    print("\n--- End of Script ---")
# Adding 5 new lines at the end as requested:
print("\n--- Additional Lines ---")
for i in range(3):
    print(f"This is line {i+1} of the new block.")