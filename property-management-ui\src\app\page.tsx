"use client";

// This is a random edit by <PERSON>ubent!

import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/main-layout";
import { DashboardStats } from "@/types";
import { apiService } from "@/services/api";
import { Building2, Users, FileText, DollarSign, TrendingUp, AlertCircle, Clock, ArrowUpRight } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DashboardMetricCard from "@/components/dashboard/DashboardMetricCard";
import { motion } from "framer-motion";

// --- Start of Cubent's Big Random Edit ---

// This is a new, dummy React component added as part of a "big random edit".
// It demonstrates basic state management and a side effect.
function RandomBigEditComponent() {
  // State to hold a random number
  const [randomNumber, setRandomNumber] = useState(0);
  // State to track if the component has mounted
  const [isMounted, setIsMounted] = useState(false);

  // useEffect hook to simulate some activity on component mount
  useEffect(() => {
    // Set isMounted to true once the component has mounted
    setIsMounted(true);
    // Generate a random number when the component mounts
    const newRandom = Math.floor(Math.random() * 1000);
    setRandomNumber(newRandom);

    // Log to console for demonstration purposes
    console.log(`RandomBigEditComponent mounted! Generated number: ${newRandom}`);

    // Cleanup function (optional, but good practice)
    return () => {
      console.log("RandomBigEditComponent unmounted.");
    };
  }, []); // Empty dependency array means this effect runs once on mount

  return (
    // A simple card to display the random number
    <Card className="bg-gradient-to-br from-purple-500 to-pink-500 text-white shadow-xl">
      <CardHeader>
        <CardTitle className="text-2xl font-bold">Cubent's Random Component</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Display a message based on mount status */}
        {isMounted ? (
          <p className="text-lg">This component is mounted and showing a random number!</p>
        ) : (
          <p className="text-lg">Loading random component...</p>
        )}
        {/* Display the generated random number */}
        <p className="text-4xl font-extrabold mt-4">{randomNumber}</p>
        <p className="text-sm opacity-80 mt-2">
          This number was generated randomly when the component loaded.
        </p>
      </CardContent>
    </Card>
  );
}

// --- End of Cubent's Big Random Edit ---

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);
      try {
        const dashboardStats = await apiService.getDashboardStats();
        setStats(dashboardStats);
      } catch (e: any) {
        setError(e.message || "Failed to fetch dashboard data");
        console.error("Dashboard fetch error:", e);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-red-700 dark:text-red-200">Error loading dashboard: {error}</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-8"
      >
        {/* Enhanced Header Section */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl p-8 text-white shadow-lg">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-2">Welcome Back!</h1>
              <p className="text-blue-100">Hope you're having a fantastic day managing properties!</p>
              <p className="text-blue-100">Here's what's happening with your properties today.</p>
            </div>
            <div className="mt-4 md:mt-0 flex items-center space-x-2 text-blue-100">
              <Clock className="h-5 w-5" />
              <span>Last updated: {new Date().toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Enhanced Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <DashboardMetricCard
              title="Total Properties"
              value={stats?.totalProperties || 0}
              icon={Building2}
              iconColor="text-blue-500"
              trend={"+2.5%"}
            />
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <DashboardMetricCard
              title="Occupancy Rate"
              value={`${stats?.occupancyRate?.toFixed(1) || 0}%`}
              icon={TrendingUp}
              iconColor="text-green-500"
              trend={"+1.2%"}
            />
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <DashboardMetricCard
              title="Total Tenants"
              value={stats?.totalTenants || 0}
              icon={Users}
              iconColor="text-purple-500"
              trend={"+3.8%"}
            />
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <DashboardMetricCard
              title="Monthly Revenue"
              value={`$${stats?.totalRent?.toLocaleString() || 0}`}
              icon={DollarSign}
              iconColor="text-yellow-500"
              trend={"+5.2%"}
            />
          </motion.div>
        </div>

        {/* Enhanced Property Status and Financial Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div
            whileHover={{ scale: 1.01 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700"
          >
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Property Status</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Occupied</span>
                <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                  {stats?.occupiedProperties || 0} properties
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Available</span>
                <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                  {stats?.availableProperties || 0} properties
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Active Leases</span>
                <span className="text-sm font-semibold text-purple-600 dark:text-purple-400">
                  {stats?.activeLeases || 0} leases
                </span>
              </div>
            </div>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.01 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700"
          >
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Financial Overview</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Average Rent</span>
                <span className="text-sm font-semibold text-gray-900 dark:text-white">
                  ${stats?.averageRent?.toFixed(2) || 0}
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Monthly Revenue</span>
                <span className="text-sm font-semibold text-gray-900 dark:text-white">
                  ${stats?.totalRent?.toLocaleString() || 0}
                </span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Enhanced Quick Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors group"
            >
              <Building2 className="h-6 w-6 text-blue-500 mr-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">Add Property</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors group"
            >
              <Users className="h-6 w-6 text-green-500 mr-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium text-green-600 dark:text-green-400">Add Tenant</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center justify-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors group"
            >
              <FileText className="h-6 w-6 text-purple-500 mr-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium text-purple-600 dark:text-purple-400">Create Lease</span>
            </motion.button>
          </div>
        </div>

        {/* Recent Activity Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Recent Activity</h3>
          <div className="space-y-4">
            <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">New lease signed for Unit 302</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">2 hours ago</p>
              </div>
              <ArrowUpRight className="h-4 w-4 text-gray-400" />
            </div>
            <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Maintenance request completed</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">5 hours ago</p>
              </div>
              <ArrowUpRight className="h-4 w-4 text-gray-400" />
            </div>
            <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Rent payment received</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">1 day ago</p>
              </div>
              <ArrowUpRight className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>

        {/* New Upcoming Events Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Upcoming Events</h3>
          <div className="space-y-4">
            <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Property inspection at 123 Main St</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Tomorrow, 10:00 AM</p>
              </div>
              <ArrowUpRight className="h-4 w-4 text-gray-400" />
            </div>
            <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Rent due for Unit 4B</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">July 15, 2025</p>
              </div>
              <ArrowUpRight className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>

        {/* New Upcoming Events Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Upcoming Events</h3>
          <div className="space-y-4">
            <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Property inspection at 123 Main St</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Tomorrow, 10:00 AM</p>
              </div>
              <ArrowUpRight className="h-4 w-4 text-gray-400" />
            </div>
            <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Rent due for Unit 4B</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">July 15, 2025</p>
              </div>
              <ArrowUpRight className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
        {/* Cubent's Big Random Component added here */}
        <RandomBigEditComponent />
      </motion.div>
    </MainLayout>
  );
}
