import React, { useState, useMemo } from 'react';

interface User {
  id: number;
  name: string;
  email: string;
}

const initialUsers: User[] = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>' },
  { id: 3, name: '<PERSON>', email: 'char<PERSON>@example.com' },
  { id: 4, name: '<PERSON>', email: '<EMAIL>' },
  { id: 5, name: '<PERSON>', email: '<EMAIL>' },
  { id: 6, name: '<PERSON>', email: '<EMAIL>' },
];

const UserSearch = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredUsers = useMemo(() => {
    return initialUsers.filter(user =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>User Search</h2>
      <input
        type="text"
        placeholder="Search users by name or email..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        style={{
          width: '100%',
          padding: '10px',
          marginBottom: '20px',
          border: '1px solid #ddd',
          borderRadius: '4px',
          boxSizing: 'border-box',
        }}
      />
      <div style={{ maxHeight: '400px', overflowY: 'auto', border: '1px solid #eee', borderRadius: '4px' }}>
        {filteredUsers.length > 0 ? (
          <ul style={{ listStyle: 'none', padding: '0', margin: '0' }}>
            {filteredUsers.map(user => (
              <li
                key={user.id}
                style={{
                  padding: '15px',
                  borderBottom: '1px solid #eee',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '5px',
                  backgroundColor: '#fff',
                }}
              >
                <strong style={{ color: '#333' }}>{user.name}</strong>
                <span style={{ color: '#666', fontSize: '0.9em' }}>{user.email}</span>
              </li>
            ))}
          </ul>
        ) : (
          <p style={{ padding: '15px', textAlign: 'center', color: '#888' }}>No users found.</p>
        )}
      </div>
    </div>
  );
};

export default UserSearch;