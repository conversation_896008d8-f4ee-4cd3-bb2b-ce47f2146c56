const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const mongoose = require('mongoose');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// MongoDB connection (optional - can work with in-memory data for now)
// mongoose.connect('mongodb://localhost:27017/property-management', {
//   useNewUrlParser: true,
//   useUnifiedTopology: true,
// });

// In-memory data store (replace with MongoDB later)
let properties = [
  {
    id: 1,
    address: '101 Pine Ave',
    rent: 1800,
    bedrooms: 3,
    bathrooms: 2,
    status: 'available',
    type: 'house',
    squareFeet: 1200,
    description: 'Charming 3-bedroom house with a spacious backyard',
    amenities: ['parking', 'garden', 'dishwasher'],
    dateAdded: new Date('2024-01-15'),
    lastUpdated: new Date('2024-12-01')
  },
  {
    id: 2,
    address: '222 Maple Dr',
    rent: 2000,
    bedrooms: 4,
    bathrooms: 3,
    status: 'occupied',
    type: 'house',
    squareFeet: 1500,
    description: 'Modern 4-bedroom family home in quiet neighborhood',
    amenities: ['parking', 'garage', 'dishwasher', 'air_conditioning'],
    dateAdded: new Date('2024-02-20'),
    lastUpdated: new Date('2024-11-15')
  },
  {
    id: 3,
    address: '777 Cherry Lane',
    rent: 2500,
    bedrooms: 5,
    bathrooms: 4,
    status: 'available',
    type: 'house',
    squareFeet: 2000,
    description: 'Luxury 5-bedroom house with premium finishes',
    amenities: ['parking', 'garage', 'dishwasher', 'air_conditioning', 'fireplace', 'pool'],
    dateAdded: new Date('2024-03-10'),
    lastUpdated: new Date('2024-12-05')
  }
];

let tenants = [
  {
    id: 1,
    name: 'John Smith',
    email: '<EMAIL>',
    phone: '(*************',
    propertyId: 2,
    leaseStart: new Date('2024-01-01'),
    leaseEnd: new Date('2024-12-31'),
    rentAmount: 2000,
    status: 'active'
  },
  {
    id: 2,
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    propertyId: null,
    leaseStart: null,
    leaseEnd: null,
    rentAmount: null,
    status: 'prospective'
  }
];

let leases = [
  {
    id: 1,
    propertyId: 2,
    tenantId: 1,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    rentAmount: 2000,
    securityDeposit: 2000,
    status: 'active',
    terms: 'Standard 12-month lease agreement'
  }
];

// API Routes

// Properties endpoints
app.get('/api/properties', (req, res) => {
  res.json(properties);
});

app.get('/api/properties/:id', (req, res) => {
  const property = properties.find(p => p.id === parseInt(req.params.id));
  if (!property) {
    return res.status(404).json({ error: 'Property not found' });
  }
  res.json(property);
});

app.post('/api/properties', (req, res) => {
  const newProperty = {
    id: Math.max(...properties.map(p => p.id)) + 1,
    ...req.body,
    dateAdded: new Date(),
    lastUpdated: new Date()
  };
  properties.push(newProperty);
  res.status(201).json(newProperty);
});

app.put('/api/properties/:id', (req, res) => {
  const index = properties.findIndex(p => p.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({ error: 'Property not found' });
  }
  properties[index] = {
    ...properties[index],
    ...req.body,
    lastUpdated: new Date()
  };
  res.json(properties[index]);
});

app.delete('/api/properties/:id', (req, res) => {
  const index = properties.findIndex(p => p.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({ error: 'Property not found' });
  }
  properties.splice(index, 1);
  res.status(204).send();
});

// Tenants endpoints
app.get('/api/tenants', (req, res) => {
  res.json(tenants);
});

app.get('/api/tenants/:id', (req, res) => {
  const tenant = tenants.find(t => t.id === parseInt(req.params.id));
  if (!tenant) {
    return res.status(404).json({ error: 'Tenant not found' });
  }
  res.json(tenant);
});

app.post('/api/tenants', (req, res) => {
  const newTenant = {
    id: Math.max(...tenants.map(t => t.id)) + 1,
    ...req.body
  };
  tenants.push(newTenant);
  res.status(201).json(newTenant);
});

app.put('/api/tenants/:id', (req, res) => {
  const index = tenants.findIndex(t => t.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({ error: 'Tenant not found' });
  }
  tenants[index] = { ...tenants[index], ...req.body };
  res.json(tenants[index]);
});

app.delete('/api/tenants/:id', (req, res) => {
  const index = tenants.findIndex(t => t.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({ error: 'Tenant not found' });
  }
  tenants.splice(index, 1);
  res.status(204).send();
});

// Leases endpoints
app.get('/api/leases', (req, res) => {
  res.json(leases);
});

app.get('/api/leases/:id', (req, res) => {
  const lease = leases.find(l => l.id === parseInt(req.params.id));
  if (!lease) {
    return res.status(404).json({ error: 'Lease not found' });
  }
  res.json(lease);
});

app.post('/api/leases', (req, res) => {
  const newLease = {
    id: Math.max(...leases.map(l => l.id)) + 1,
    ...req.body
  };
  leases.push(newLease);
  res.status(201).json(newLease);
});

app.put('/api/leases/:id', (req, res) => {
  const index = leases.findIndex(l => l.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({ error: 'Lease not found' });
  }
  leases[index] = { ...leases[index], ...req.body };
  res.json(leases[index]);
});

app.delete('/api/leases/:id', (req, res) => {
  const index = leases.findIndex(l => l.id === parseInt(req.params.id));
  if (index === -1) {
    return res.status(404).json({ error: 'Lease not found' });
  }
  leases.splice(index, 1);
  res.status(204).send();
});

// Dashboard analytics endpoint
app.get('/api/dashboard/stats', (req, res) => {
  const totalProperties = properties.length;
  const occupiedProperties = properties.filter(p => p.status === 'occupied').length;
  const availableProperties = properties.filter(p => p.status === 'available').length;
  const totalRent = properties.reduce((sum, p) => sum + p.rent, 0);
  const averageRent = totalRent / totalProperties || 0;
  const occupancyRate = (occupiedProperties / totalProperties) * 100 || 0;

  res.json({
    totalProperties,
    occupiedProperties,
    availableProperties,
    totalRent,
    averageRent,
    occupancyRate,
    totalTenants: tenants.length,
    activeLeases: leases.filter(l => l.status === 'active').length
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

app.listen(PORT, () => {
  console.log(`Property Management API server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});

module.exports = app;