@@ -8,5 +8,13 @@
 };
 export const capitalizeString = (str: string): string => {
   if (!str) return '';
   return str.charAt(0).toUpperCase() + str.slice(1);
+};
+export const formatDate = (date: Date | string, locale: string = 'en-US'): string => {
+  const d = new Date(date);
+  return new Intl.DateTimeFormat(locale, {
+    year: 'numeric',
+    month: 'long',
+    day: 'numeric',
+  }).format(d);
 };
\ No newline at end of file
