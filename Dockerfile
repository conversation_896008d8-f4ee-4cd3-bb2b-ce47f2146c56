# Stage 1: Builder
FROM node:20-slim AS builder

WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci

COPY . .

# Build the React application
# Example of build-time environment variable (if your app uses them, e.g., REACT_APP_API_URL)
# ENV REACT_APP_API_URL=http://localhost:3001/api
RUN npm run build
#kdoe

# Stage 2: Production
FROM nginx:latest

# Copy custom Nginx configuration (optional, but good for production)
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy the built React app from the builder stage
COPY --from=builder /app/build /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]