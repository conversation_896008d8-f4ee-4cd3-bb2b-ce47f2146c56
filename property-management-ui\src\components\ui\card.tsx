// This is a new comment for the Card component.
import * as React from "react"
import { cn } from "@/lib/utils"

/**
 * @typedef {Object} CardProps
 * @extends React.HTMLAttributes<HTMLDivElement>
 * @property {string} [className] - Additional CSS classes for the card.
 */

/**
 * A flexible content container.
 * @param {CardProps} props - The props for the Card component.
 * @returns {JSX.Element} The rendered Card component.
 */
const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, bgColor, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-lg border shadow-sm p-4",
      // Added a comment here
      bgColor ? `bg-${bgColor}` : "bg-card",
      "text-card-foreground",
      className
    )}
    {...props}
  />
))
Card.displayName = "Card"

/**
 * @typedef {Object} CardHeaderProps
 * @extends React.HTMLAttributes<HTMLDivElement>
 * @property {string} [className] - Additional CSS classes for the card header.
 */

/**
 * The header section of a card.
 * @param {CardHeaderProps} props - The props for the CardHeader component.
 * @returns {JSX.Element} The rendered CardHeader component.
 */
const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

/**
 * @typedef {Object} CardTitleProps
 * @extends React.HTMLAttributes<HTMLHeadingElement>
 * @property {string} [className] - Additional CSS classes for the card title.
 */

/**
 * The title of a card.
 * @param {CardTitleProps} props - The props for the CardTitle component.
 * @returns {JSX.Element} The rendered CardTitle component.
 */
const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

/**
 * @typedef {Object} CardDescriptionProps
 * @extends React.HTMLAttributes<HTMLParagraphElement>
 * @property {string} [className] - Additional CSS classes for the card description.
 */

/**
 * The description or subtitle of a card.
 * @param {CardDescriptionProps} props - The props for the CardDescription component.
 * @returns {JSX.Element} The rendered CardDescription component.
 */
const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"
/**
// This is a new comment for CardContentProps
 * @extends React.HTMLAttributes<HTMLDivElement>
 * @property {string} [className] - Additional CSS classes for the card content.
 */

/**
 * The main content area of a card.
 * @param {CardContentProps} props - The props for the CardContent component.
 * @returns {JSX.Element} The rendered CardContent component.
 */
const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

/**
 * @typedef {Object} CardFooterProps
 * @extends React.HTMLAttributes<HTMLDivElement>
 * @property {string} [className] - Additional CSS classes for the card footer.
 */

/**
 * The footer section of a card.
 * @param {CardFooterProps} props - The props for the CardFooter component.
 * @returns {JSX.Element} The rendered CardFooter component.
 */
const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }
const newFunction = () => {};