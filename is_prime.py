import random

def is_prime(n):
    """
    This function checks if a number is prime.
    Now with a fun fact!
    """
    # Fun fact: 2 is the only even prime number.
    if n <= 1:
        return False
    for i in range(2, int(n**0.5) + 1):
        if n % i == 0:
            return False
    return True

def random_prime_in_range(start, end):
    """
    Returns a random prime number in the given range [start, end].
    If no prime is found, returns None.
    """
    primes = [x for x in range(start, end+1) if is_prime(x)]
    return random.choice(primes) if primes else None

if __name__ == "__main__":
    print("Fun fact: 2 is the only even prime number!")
    print(f"Is 2 prime? {is_prime(2)}")
    print(f"Is 7 prime? {is_prime(7)}")
    print(f"Is 10 prime? {is_prime(10)}")
    print(f"Is 29 prime? {is_prime(29)}")
    print(f"Is 1 prime? {is_prime(1)}")
    print(f"Is 0 prime? {is_prime(0)}")
    print("\nRandom prime between 10 and 50:")
    print(random_prime_in_range(10, 50))
    print("\nRandom prime between 100 and 120:")
    print(random_prime_in_range(100, 120))