import React from 'react';
import { Search, Bell, HelpCircle, Plus, ChevronDown } from 'lucide-react'; // Placeholder for Remix Icon
import { Button } from '@/components/ui/button'; // Assuming ShadCN Button component

interface TopbarProps {
  pageTitle: string;
}

const Topbar: React.FC<TopbarProps> = ({ pageTitle }) => {
  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm p-4 flex items-center justify-between">
      <h1 className="text-xl font-semibold text-gray-800 dark:text-white">{pageTitle}</h1>

      <div className="flex items-center space-x-4">
        {/* Organization Switcher Placeholder */}
        <div className="flex items-center cursor-pointer">
          <span className="text-gray-700 dark:text-gray-300">My Organization</span>
          <ChevronDown size={16} className="ml-1 text-gray-500 dark:text-gray-400" />
        </div>

        {/* Search Bar Placeholder */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search..."
            className="pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
          />
          <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>

        {/* Icons */}
        <Bell size={20} className="text-gray-500 dark:text-gray-400 cursor-pointer hover:text-accent transition-colors" />
        <HelpCircle size={20} className="text-gray-500 dark:text-gray-400 cursor-pointer hover:text-accent transition-colors" />

        {/* Add New Property Button */}
        <Button className="bg-accent hover:bg-green-600 text-white rounded-button px-4 py-2 flex items-center">
          <Plus size={18} className="mr-2" />
          Add new property
        </Button>
      </div>
    </header>
  );
};

export default Topbar;