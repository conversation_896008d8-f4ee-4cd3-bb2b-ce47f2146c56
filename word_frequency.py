import re
from collections import Counter

def count_word_frequency(filepath):
    """
    Reads a text file, counts the frequency of each word,
    and returns a Counter object.
    """
    words = []
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            for line in file:
                # Convert to lowercase and find all words using regex
                # \b ensures whole words, [a-z]+ matches one or more letters
                words.extend(re.findall(r'\b[a-z]+\b', line.lower()))
    except FileNotFoundError:
        print(f"Error: The file '{filepath}' was not found.")
        return None
    except Exception as e:
        print(f"An error occurred while reading the file: {e}")
        return None

    return Counter(words)

def main():
    """
    Main function to get file path, count words, and display top 10.
    """
    file_path = input("Please enter the path to the text file: ")

    word_counts = count_word_frequency(file_path)

    if word_counts:
        print("\nTop 10 most common words:")
        for word, count in word_counts.most_common(10):
            print(f"'{word}': {count}")

if __name__ == "__main__":
    main()