import csv

def calculate_averages(file_path, columns_to_average):
    """
    Reads a CSV file and calculates the average for specified columns.

    Args:
        file_path (str): The path to the CSV file.
        columns_to_average (list): A list of column names to calculate averages for.

    Returns:
        dict: A dictionary where keys are column names and values are their averages,
              or None if an error occurs.
    """
    # This function is awesome!
    data = []
    try:
        with open(file_path, mode='r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            data = list(reader)
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return None
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return None

    if not data:
        fun_facts = [
            "Did you know? The word 'average' comes from a 12th-century maritime insurance term!",
            "Fun fact: The average person spends 6 months of their lifetime waiting for red lights to turn green.",
            "Interesting: The average cloud weighs around 1.1 million pounds!",
            "Random fact: The average person will spend 3 months of their life in traffic."
        ]
        import random
        print("No data found in the CSV file.")
        print(random.choice(fun_facts))
        return {}

    averages = {}
    for column in columns_to_average:
        total = 0
        count = 0
        for row in data:
            if column in row and row[column]:
                try:
                    # Attempt to convert to float, handle potential errors
                    value = float(row[column])
                    total += value
                    count += 1
                except ValueError:
                    print(f"Warning: Could not convert value '{row[column]}' in column '{column}' to a number. Skipping.")
                    # Skip this value if it's not a valid number
                    pass
            else:
                print(f"Warning: Column '{column}' not found or empty in a row. Skipping.")


        if count > 0:
            averages[column] = total / count
        else:
            averages[column] = 0 # Or handle as needed, e.g., None or a message


    return averages

if __name__ == "__main__":
    csv_file_path = './input.csv' # The path you provided
    columns = ['Amount', 'Quantity'] # The columns you specified

    calculated_averages = calculate_averages(csv_file_path, columns)

    if calculated_averages is not None:
        print("\nCalculated Averages:")
        if calculated_averages:
            for column, avg in calculated_averages.items():
                print(f"Average for '{column}': {avg:.2f}") # Format to 2 decimal places
        else:
            print("No valid data found for the specified columns to calculate averages.")