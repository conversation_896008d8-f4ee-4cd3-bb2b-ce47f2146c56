import { Tenant } from '../types/tenant';

export const initialTenants: Tenant[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    propertyId: 1,
    leaseStartDate: new Date('2023-01-15'),
    leaseEndDate: new Date('2024-01-14'),
    rentAmount: 1200,
    status: 'active',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    propertyId: 2,
    leaseStartDate: new Date('2023-03-01'),
    leaseEndDate: new Date('2024-02-29'),
    rentAmount: 1500,
    status: 'active',
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    propertyId: 3,
    leaseStartDate: new Date('2022-11-01'),
    leaseEndDate: new Date('2023-10-31'),
    rentAmount: 950,
    status: 'inactive',
  },
];