import React from 'react';
import { User } from 'lucide-react';

interface TenantCardProps { // Defines the structure for tenant data
  tenant: {
    id: string;
    name: string;
    email: string;
    phone: string;
    propertyName: string;
    leaseEnd: string;
  };
}

export default function TenantCard({ tenant }: TenantCardProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{tenant.name}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">{tenant.email}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm text-gray-600 dark:text-gray-300">Phone: {tenant.phone}</p>
            <p className="text-sm text-gray-600 dark:text-gray-300">Property: {tenant.propertyName}</p>
            <p className="text-sm text-gray-600 dark:text-gray-300">Lease Ends: {tenant.leaseEnd}</p>
          </div>
        </div>
        <User className="text-gray-400 dark:text-gray-500" size={24} />
      </div>
    </div>
  );
}
