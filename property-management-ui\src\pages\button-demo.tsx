// This is a random edit!
import React from "react";
import { <PERSON><PERSON> } from "../components/ui/button";
import ButtonVariantsSection from "../components/ui/ButtonVariantsSection";

export default function ButtonDemo() {
  // This component demonstrates various button styles and props.
  // This div provides padding and centers the content on the page.
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Interactive Button Component Showcase</h1>
      
      <div className="space-y-8">
        <ButtonVariantsSection />

        <section>
          <h2 className="text-xl font-semibold mb-4">New Prop Examples</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="secondary">Click Me</Button>
            <Button isBold>Bold Prop</Button>
            <Button isRounded>Rounded Prop</Button>
            <Button isWide>Wide Prop</Button>
            <Button isSmallText>Small Text Prop</Button>
            <Button variant="outline" isBold isRounded>Outline Bold Rounded</Button>
            <Button variant="destructive" isBold>Destructive Bold</Button>
            <Button variant="primary" isRounded isSmallText>🚀 New Feature</Button>
          </div>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-4">Combined Examples with New Props</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="secondary" isRounded isWide>
              Rounded Wide Secondary
            </Button>
            <Button variant="danger" isBold isSmallText>
              Bold Small Danger
            </Button>
            <Button variant="info" isRounded isWide>
              Info Rounded Wide
            </Button>
            <Button variant="success" isWide isSmallText isRounded>
              Small Wide Rounded Success
            </Button>
&lt;Button
              style={{
                backgroundColor: '#' + Math.floor(Math.random() * 16777215).toString(16), // Random background color
                color: 'white', // White text for contrast
                padding: '10px 20px', // Add some padding
                borderRadius: '5px', // Rounded corners
                border: 'none', // Remove border
                cursor: 'pointer', // Change cursor to pointer on hover
              }}
              onClick={() => alert('This is a randomly styled button!')} // Add an alert on click
            >
              Random Button
            &lt;/Button>
            {/* This button is dynamically styled with a random background color.
                It demonstrates how inline styles can be used to quickly create
                unique button appearances. The onClick handler shows a simple alert
                to provide immediate feedback to the user. */}
          </div>
        </section>
      </div>
    </div>
  );
}