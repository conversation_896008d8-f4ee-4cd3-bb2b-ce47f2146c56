/* App.css edited by <PERSON><PERSON><PERSON>! Adding some styles! ✨ */
/* Adding a random comment to the CSS */
body {
  font-family: sans-serif;
  margin: 0;
  padding: 20px;
}

h1 {
  text-align: center;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border-radius: 10px;
  padding: 32px 24px 24px 24px;
  margin-top: 32px;
}

.search-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.search-input {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  width: 300px;
  font-size: 16px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

.property-list {
  margin-top: 24px;
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.property-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.property-item:last-child {
  border-bottom: none;
}

.add-property-form {
  margin: 24px 0;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.add-property-form input,
.add-property-form button {
  padding: 8px 12px;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-size: 15px;
}

.add-property-form button {
  background: #007bff;
  color: #fff;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
}

.add-property-form button:hover {
  background: #0056b3;
}

.no-properties {
  text-align: center;
  color: #888;
  margin-top: 32px;
  font-size: 18px;
}

.price-range-filter {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.price-range-filter label {
  margin-right: 10px;
}

.price-range-filter input {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  width: 100px;
  font-size: 16px;
}

.price-range-filter input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

.app-container {
 background-color: #f0f8ff; /* Light blue background added by Qapt */
 padding: 20px;
 border-radius: 8px;
}