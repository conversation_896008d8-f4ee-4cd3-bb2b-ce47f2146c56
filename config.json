{"name": "property-management-software", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.7.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^2.0.3", "@radix-ui/react-calendar": "^1.0.3", "@radix-ui/react-card": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@uploadthing/react": "^6.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "next": "14.0.4", "next-auth": "^4.24.5", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.0.4", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "prisma": "^5.7.1", "tailwindcss": "^3.3.0", "typescript": "^5"}, "randomEdit": true, "newFeature": true, "randomEditComment": "This is a random edit made by the AI.", "random_key": "random_value", "featureFlags": {"newFeature": true}, "cubent_random_addition": "hello from cubent"}