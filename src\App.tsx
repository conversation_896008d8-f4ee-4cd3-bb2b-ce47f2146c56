// Another random edit: This is a new comment added for demonstration.
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PropertyList from './components/PropertyList';
import Navbar from './components/Navbar';
import UserSearch from './components/UserSearch';

function App() {
  // This is a new random edit!
  return (
    
    <Router>
      <div className="min-h-screen bg-gray-100">
        {/* Display a welcoming message to the user */}
        <p className="text-lg font-semibold text-center my-4">Hello there! Let's manage some properties.</p>
        
        {/* Main content area for the application */}
        <main className="container mx-auto px-4 py-8">
          {/* The Navbar component provides navigation links */}
          <Navbar />
          {/* Routes define the different pages and their corresponding components */}
          <Routes>
            <Route path="/" element={<PropertyList />} />
          </Routes>
          <UserSearch />
        </main>
      </div>
    </Router>
  );
  
};

const Greeting = () => {
  return <h1>Hello, world!</h1>;
};

export default App;
