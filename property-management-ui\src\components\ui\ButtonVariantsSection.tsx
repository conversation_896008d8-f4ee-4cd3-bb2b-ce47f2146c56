// This file demonstrates various button variants.
import React from "react";
import { But<PERSON> } from "./button";

export default function ButtonVariantsSection() {
  return (
    <section>
      <h2 className="text-xl font-semibold mb-4">Explore Button Variants</h2>
      <div className="flex flex-wrap gap-4">
        <Button variant="primary">Primary Button</Button>
        <Button variant="secondary">Secondary Button</Button>
        <Button variant="danger">Danger Button</Button>
        <Button variant="success">Success Button</Button>
        <Button variant="warning">Warning Button</Button>
        <Button variant="info">Info Button</Button>
      </div>
    </section>
  );
}
console.log("New context added!");