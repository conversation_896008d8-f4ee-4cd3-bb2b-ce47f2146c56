import { Property, PropertyFormData } from '../types/property';

export const initialProperties: Property[] = [
  { 
    id: 4, 
    address: '101 Pine Ave', 
    rent: 1800, 
    bedrooms: 3, 
    bathrooms: 2,
    status: 'available',
    type: 'house',
    squareFeet: 1200,
    description: 'Charming 3-bedroom house with a spacious backyard',
    amenities: ['parking', 'garden', 'dishwasher'],
    dateAdded: new Date('2024-01-15'),
    lastUpdated: new Date('2024-12-01')
  },
  { 
    id: 5, 
    address: '222 Maple Dr', 
    rent: 2000, 
    bedrooms: 4, 
    bathrooms: 3,
    status: 'occupied',
    type: 'house',
    squareFeet: 1500,
    description: 'Modern 4-bedroom family home in quiet neighborhood',
    amenities: ['parking', 'garage', 'dishwasher', 'air_conditioning'],
    dateAdded: new Date('2024-02-20'),
    lastUpdated: new Date('2024-11-15')
  },
  { 
    id: 6, 
    address: '777 Cherry Lane', 
    rent: 2500, 
    bedrooms: 5, 
    bathrooms: 4,
    status: 'available',
    type: 'house',
    squareFeet: 2000,
    description: 'Luxury 5-bedroom house with premium finishes',
    amenities: ['parking', 'garage', 'dishwasher', 'air_conditioning', 'fireplace', 'pool'],
    dateAdded: new Date('2024-03-10'),
    lastUpdated: new Date('2024-12-05')
  },
  {
    id: 7,
    address: '888 Ocean View Blvd',
    rent: 3500,
    bedrooms: 4,
    bathrooms: 3.5,
    status: 'available',
    type: 'condo',
    squareFeet: 2200,
    description: 'Stunning oceanfront condo with panoramic views and modern amenities. Perfect for luxury living.',
    amenities: ['balcony', 'gym', 'pool', 'concierge', 'parking', 'pet_friendly'],
    dateAdded: new Date('2024-05-01'),
    lastUpdated: new Date('2024-06-08')
  },
];

export default initialProperties;