import React from 'react';
import Sidebar from '@/components/sidebar/sidebar';
import Topbar from '@/components/topbar/topbar';
import ErrorBoundary from '@/components/error-boundary';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div className="flex min-h-screen bg-white dark:bg-black">
      <Sidebar />

      <div className="flex-1 flex flex-col ml-64"> {/* Adjusted margin for sidebar */}
        <Topbar pageTitle="Main Dashboard" />



        <main className="flex-1 p-6 overflow-y-auto">
          <ErrorBoundary>
            {children}
          </ErrorBoundary>
        </main>
      </div>
    </div>
  );
};

export default MainLayout;