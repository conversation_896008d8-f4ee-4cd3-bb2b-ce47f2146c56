#!/bin/bash

# backup_script.sh - Backs up a specified directory into a timestamped archive.

# --- Configuration ---
LOG_DIR="backup_logs"
BACKUP_DIR="backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/backup_${TIMESTAMP}.log"

# --- Functions ---

# Function to log messages
log_message() {
    local type="$1"
    local message="$2"
    echo "$(date +"%Y-%m-%d %H:%M:%S") [${type}] ${message}" | tee -a "${LOG_FILE}"
}

# Function to display usage
display_usage() {
    echo "Usage: $0 <directory_to_backup>"
    echo "Example: $0 /home/<USER>/documents"
    exit 1
}

# --- Main Script ---

# Create log and backup directories if they don't exist
mkdir -p "${LOG_DIR}"
mkdir -p "${BACKUP_DIR}"

log_message "INFO" "Backup process started."
log_message "INFO" "Log file: ${LOG_FILE}"

# Check if a directory was provided
if [ -z "$1" ]; then
    log_message "ERROR" "No directory specified for backup."
    display_usage
fi

SOURCE_DIR="$1"
ARCHIVE_NAME="backup_$(basename "${SOURCE_DIR}")_${TIMESTAMP}.tar.gz"
ARCHIVE_PATH="${BACKUP_DIR}/${ARCHIVE_NAME}"

# Check if the source directory exists
if [ ! -d "${SOURCE_DIR}" ]; then
    log_message "ERROR" "Source directory '${SOURCE_DIR}' does not exist."
    exit 1
fi

log_message "INFO" "Attempting to backup '${SOURCE_DIR}' to '${ARCHIVE_PATH}'"

# Perform the backup
tar -czf "${ARCHIVE_PATH}" -C "$(dirname "${SOURCE_DIR}")" "$(basename "${SOURCE_DIR}")"

# Check the exit status of the tar command
if [ $? -eq 0 ]; then
    log_message "SUCCESS" "Backup of '${SOURCE_DIR}' completed successfully."
    log_message "INFO" "Archive created: '${ARCHIVE_PATH}'"
    log_message "INFO" "Backup process finished."
else
    log_message "ERROR" "Backup of '${SOURCE_DIR}' failed."
    log_message "ERROR" "Please check the error messages above for details."
    exit 1
fi