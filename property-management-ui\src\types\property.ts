export interface Property {
  id: number;
  address: string;
  rent: number;
  bedrooms: number;
  bathrooms: number;
  status?: 'available' | 'occupied' | 'maintenance';
  type?: 'apartment' | 'house' | 'condo' | 'townhouse';
  squareFeet?: number;
  description?: string;
  amenities?: string[];
  dateAdded?: Date;
  lastUpdated?: Date;
}

export interface PropertyFormData {
  address: string;
  rent: number;
  bedrooms: number;
  bathrooms: number;
  status: 'available' | 'occupied' | 'maintenance';
  type: 'apartment' | 'house' | 'condo' | 'townhouse';
  squareFeet?: number;
  description?: string;
  amenities?: string[];
}